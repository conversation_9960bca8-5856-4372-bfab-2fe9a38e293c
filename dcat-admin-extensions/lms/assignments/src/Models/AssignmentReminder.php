<?php

namespace Lms\Assignments\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\School;

class AssignmentReminder extends Model
{
    protected $table = 'lms_assignment_reminders';

    protected $fillable = [
        'assignment_id', 'student_id', 'reminder_type', 'scheduled_at',
        'delivery_method', 'subject', 'message', 'template_data', 'status',
        'sent_at', 'error_message', 'retry_count', 'created_by', 'school_id'
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'template_data' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    // Relationships
    public function assignment(): BelongsTo
    {
        return $this->belongsTo(Assignment::class, 'assignment_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeDue($query)
    {
        return $query->where('scheduled_at', '<=', now())
                    ->where('status', 'scheduled');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeForAssignment($query, int $assignmentId)
    {
        return $query->where('assignment_id', $assignmentId);
    }

    public function scopeForStudent($query, int $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('reminder_type', $type);
    }

    // Business Logic Methods
    public function isDue(): bool
    {
        return $this->scheduled_at <= now() && $this->status === 'scheduled';
    }

    public function canRetry(): bool
    {
        return $this->status === 'failed' && $this->retry_count < 3;
    }

    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'error_message' => null,
        ]);
    }

    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    public function getProcessedMessage(): string
    {
        $message = $this->message;
        $templateData = $this->template_data ?? [];

        // Replace template variables
        foreach ($templateData as $key => $value) {
            $message = str_replace("{{$key}}", $value, $message);
        }

        // Replace common variables
        if ($this->assignment) {
            $message = str_replace('{assignment_title}', $this->assignment->title, $message);
            $message = str_replace('{due_date}', $this->assignment->due_date->format('M j, Y g:i A'), $message);
            $message = str_replace('{teacher_name}', $this->assignment->teacher->name, $message);
        }

        if ($this->student) {
            $message = str_replace('{student_name}', $this->student->name, $message);
        }

        return $message;
    }

    public function getProcessedSubject(): string
    {
        $subject = $this->subject;
        $templateData = $this->template_data ?? [];

        // Replace template variables
        foreach ($templateData as $key => $value) {
            $subject = str_replace("{{$key}}", $value, $subject);
        }

        // Replace common variables
        if ($this->assignment) {
            $subject = str_replace('{assignment_title}', $this->assignment->title, $subject);
        }

        return $subject;
    }

    public static function createDueSoonReminder(Assignment $assignment, ?User $student = null, int $daysBefore = 1): self
    {
        $scheduledAt = $assignment->due_date->subDays($daysBefore);

        return self::create([
            'assignment_id' => $assignment->id,
            'student_id' => $student?->id,
            'reminder_type' => 'due_soon',
            'scheduled_at' => $scheduledAt,
            'delivery_method' => 'email',
            'subject' => 'Assignment Due Soon: {assignment_title}',
            'message' => 'Hi {student_name}, your assignment "{assignment_title}" is due on {due_date}. Please make sure to submit it on time.',
            'template_data' => [
                'days_until_due' => $daysBefore,
            ],
            'created_by' => Auth::id(),
            'school_id' => $assignment->school_id,
        ]);
    }

    public static function createOverdueReminder(Assignment $assignment, User $student): self
    {
        return self::create([
            'assignment_id' => $assignment->id,
            'student_id' => $student->id,
            'reminder_type' => 'overdue',
            'scheduled_at' => now(),
            'delivery_method' => 'email',
            'subject' => 'Overdue Assignment: {assignment_title}',
            'message' => 'Hi {student_name}, your assignment "{assignment_title}" was due on {due_date} and is now overdue. Please submit it as soon as possible.',
            'created_by' => Auth::id(),
            'school_id' => $assignment->school_id,
        ]);
    }

    public static function createGradedReminder(Assignment $assignment, User $student): self
    {
        return self::create([
            'assignment_id' => $assignment->id,
            'student_id' => $student->id,
            'reminder_type' => 'graded',
            'scheduled_at' => now(),
            'delivery_method' => 'email',
            'subject' => 'Assignment Graded: {assignment_title}',
            'message' => 'Hi {student_name}, your assignment "{assignment_title}" has been graded. Please check your submission for feedback.',
            'created_by' => Auth::id(),
            'school_id' => $assignment->school_id,
        ]);
    }

    public static function createFeedbackAvailableReminder(Assignment $assignment, User $student): self
    {
        return self::create([
            'assignment_id' => $assignment->id,
            'student_id' => $student->id,
            'reminder_type' => 'feedback_available',
            'scheduled_at' => now(),
            'delivery_method' => 'email',
            'subject' => 'Feedback Available: {assignment_title}',
            'message' => 'Hi {student_name}, feedback for your assignment "{assignment_title}" is now available. Please review the comments and suggestions.',
            'created_by' => Auth::id(),
            'school_id' => $assignment->school_id,
        ]);
    }

    public static function getReminderTypes(): array
    {
        return [
            'due_soon' => 'Due Soon',
            'overdue' => 'Overdue',
            'graded' => 'Graded',
            'feedback_available' => 'Feedback Available',
            'custom' => 'Custom',
        ];
    }

    public static function getDeliveryMethods(): array
    {
        return [
            'email' => 'Email',
            'sms' => 'SMS',
            'push' => 'Push Notification',
            'in_app' => 'In-App Notification',
        ];
    }

    public static function getStatusOptions(): array
    {
        return [
            'scheduled' => 'Scheduled',
            'sent' => 'Sent',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
        ];
    }
}
